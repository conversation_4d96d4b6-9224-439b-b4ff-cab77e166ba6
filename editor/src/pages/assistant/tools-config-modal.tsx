import { Modal, Form } from 'react-bootstrap';
import { styled } from '@topwrite/common';
import { useState, useCallback } from 'react';
import { BsChevronDown, BsChevronRight, BsTrash } from 'react-icons/bs';
import { VscSettingsGear } from 'react-icons/vsc';
import { GoPlus } from 'react-icons/go';
import Button from '../../components/button';
import { useContext } from './context';

interface ToolsConfigModalProps {
    show: boolean;
    onHide: () => void;
}

export default function ToolsConfigModal({ show, onHide }: ToolsConfigModalProps) {
    const {
        builtinTools,
        mcpServers,
        toggleBuiltinTool,
        toggleAllBuiltinTools,
        toggleMcpServer,
        toggleMcpTool,
        toggleMcpServerTools,
        addMcpServer,
        deleteMcpServer,
        saveToolsConfig
    } = useContext();
    
    const [showAddMcpModal, setShowAddMcpModal] = useState(false);
    const [builtinToolsExpanded, setBuiltinToolsExpanded] = useState(true);
    const [mcpServersExpanded, setMcpServersExpanded] = useState<Record<string, boolean>>({});

    // 处理 MCP 服务器展开/折叠
    const handleToggleMcpServer = useCallback(async (serverName: string) => {
        const isExpanded = mcpServersExpanded[serverName];
        setMcpServersExpanded(prev => ({
            ...prev,
            [serverName]: !isExpanded
        }));

        // 如果是首次展开，调用原来的 toggleMcpServer 来加载工具
        if (!isExpanded) {
            await toggleMcpServer(serverName);
        }
    }, [mcpServersExpanded, toggleMcpServer]);

    // 处理模态框关闭，保存配置
    const handleClose = useCallback(async () => {
        await saveToolsConfig();
        onHide();
    }, [saveToolsConfig, onHide]);

    const builtinEnabledCount = builtinTools.filter(t => t.enabled).length;
    
    return (
        <>
            <Modal show={show} onHide={handleClose} size="lg" scrollable>
                <Modal.Header closeButton>
                    <Modal.Title as="h6" className="d-flex align-items-center">
                        <VscSettingsGear className="me-2" />
                        工具配置
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <ToolsContainer>
                        {/* 内置工具区域 */}
                        <ToolGroupContainer>
                            <GroupHeader $expanded={builtinToolsExpanded} onClick={() => setBuiltinToolsExpanded(!builtinToolsExpanded)}>
                                <GroupHeaderLeft>
                                    {builtinToolsExpanded ? <BsChevronDown /> : <BsChevronRight />}
                                    <Form.Check
                                        type="checkbox"
                                        checked={builtinEnabledCount === builtinTools.length}
                                        ref={(el: HTMLInputElement | null) => {
                                            if (el) {
                                                el.indeterminate = builtinEnabledCount > 0 && builtinEnabledCount < builtinTools.length;
                                            }
                                        }}
                                        onClick={(e) => e.stopPropagation()}
                                        onChange={(e) => {
                                            e.stopPropagation();
                                            toggleAllBuiltinTools(e.target.checked);
                                        }}
                                        title={builtinEnabledCount === builtinTools.length ? '全部关闭' : builtinEnabledCount === 0 ? '全部开启' : '全部开启'}
                                    />
                                    <GroupTitle>内置工具</GroupTitle>
                                    <ToolCount>({builtinEnabledCount}/{builtinTools.length})</ToolCount>
                                </GroupHeaderLeft>
                            </GroupHeader>
                            
                            {builtinToolsExpanded && (
                                <ToolsList>
                                    {builtinTools.map(tool => (
                                        <ToolItem key={tool.name}>
                                            <ToolToggle>
                                                <Form.Check
                                                    type="checkbox"
                                                    checked={tool.enabled}
                                                    onChange={() => toggleBuiltinTool(tool.name)}
                                                />
                                            </ToolToggle>
                                            <ToolInfo>
                                                <ToolName>{tool.title}</ToolName>
                                                <ToolDescription>{tool.description}</ToolDescription>
                                            </ToolInfo>
                                        </ToolItem>
                                    ))}
                                </ToolsList>
                            )}
                        </ToolGroupContainer>

                        {/* MCP 服务器区域 */}
                        {mcpServers.map(server => {
                            const isExpanded = mcpServersExpanded[server.name] || false;
                            const enabledCount = server.tools.filter(tool => tool.enabled).length;

                            return (
                                <ToolGroupContainer key={server.name}>
                                    <GroupHeader $expanded={isExpanded} onClick={() => handleToggleMcpServer(server.name)}>
                                        <GroupHeaderLeft>
                                            {isExpanded ? <BsChevronDown /> : <BsChevronRight />}
                                            <Form.Check
                                                type="checkbox"
                                                checked={enabledCount === server.tools.length}
                                                ref={(el: HTMLInputElement | null) => {
                                                    if (el) {
                                                        el.indeterminate = enabledCount > 0 && enabledCount < server.tools.length;
                                                    }
                                                }}
                                                onClick={(e) => e.stopPropagation()}
                                                onChange={(e) => {
                                                    e.stopPropagation();
                                                    toggleMcpServerTools(server.name, e.target.checked);
                                                }}
                                                title={enabledCount === server.tools.length ? '全部关闭' : enabledCount === 0 ? '全部开启' : '全部开启'}
                                            />
                                            <GroupTitle>{server.name}</GroupTitle>
                                            <ToolCount>({enabledCount}/{server.tools.length})</ToolCount>
                                        </GroupHeaderLeft>
                                    <GroupActions onClick={e => e.stopPropagation()}>
                                        <DeleteButton
                                            onClick={async () => {
                                                try {
                                                    await deleteMcpServer(server.name);
                                                } catch (error) {
                                                    console.error('Failed to delete MCP server:', error);
                                                }
                                            }}
                                            title="删除服务器"
                                        >
                                            <BsTrash />
                                        </DeleteButton>
                                    </GroupActions>
                                    </GroupHeader>

                                    {isExpanded && (
                                        <ToolsList>
                                            {server.tools.map(tool => (
                                                <ToolItem key={tool.name}>
                                                    <ToolToggle>
                                                        <Form.Check
                                                            type="checkbox"
                                                            checked={tool.enabled}
                                                            onChange={() => toggleMcpTool(server.name, tool.name)}
                                                        />
                                                    </ToolToggle>
                                                    <ToolInfo>
                                                        <ToolName>{tool.title}</ToolName>
                                                        <ToolDescription>{tool.description}</ToolDescription>
                                                    </ToolInfo>
                                                </ToolItem>
                                            ))}
                                        </ToolsList>
                                    )}
                                </ToolGroupContainer>
                            );
                        })}
                    </ToolsContainer>
                </Modal.Body>
                <Modal.Footer className="d-flex justify-content-between align-items-center">
                    <Button
                        variant="outline-primary"
                        onClick={() => setShowAddMcpModal(true)}
                        className="d-flex align-items-center"
                    >
                        <GoPlus className="me-1" />
                        添加 MCP 服务器
                    </Button>
                    <div className="d-flex gap-2">
                        <Button variant="secondary" onClick={handleClose}>
                            取消
                        </Button>
                        <Button onClick={handleClose}>
                            确定
                        </Button>
                    </div>
                </Modal.Footer>
            </Modal>
            
            {/* 添加 MCP 服务器弹窗 */}
            <AddMcpModal 
                show={showAddMcpModal}
                onHide={() => setShowAddMcpModal(false)}
                onAdd={addMcpServer}
            />
        </>
    );
}

// 添加 MCP 服务器弹窗组件
function AddMcpModal({ show, onHide, onAdd }: { 
    show: boolean; 
    onHide: () => void; 
    onAdd: (serverName: string, serverUrl: string) => Promise<void>; 
}) {
    const [serverUrl, setServerUrl] = useState('');
    const [serverName, setServerName] = useState('');

    const handleAdd = async () => {
        if (serverName && serverUrl) {
            await onAdd(serverName, serverUrl);
            setServerUrl('');
            setServerName('');
            onHide();
        }
    };

    return (
        <Modal show={show} onHide={onHide} centered>
            <Modal.Header closeButton>
                <Modal.Title as="h6">添加 MCP 服务器</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="mb-3">
                    <label className="form-label">服务器名称</label>
                    <input
                        type="text"
                        className="form-control"
                        value={serverName}
                        onChange={e => setServerName(e.target.value)}
                        placeholder="输入服务器名称"
                    />
                </div>
                <div className="mb-3">
                    <label className="form-label">服务器地址</label>
                    <input
                        type="text"
                        className="form-control"
                        value={serverUrl}
                        onChange={e => setServerUrl(e.target.value)}
                        placeholder="输入服务器 URL 或命令"
                    />
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={onHide}>
                    取消
                </Button>
                <Button 
                    onClick={handleAdd}
                    disabled={!serverName || !serverUrl}
                >
                    添加
                </Button>
            </Modal.Footer>
        </Modal>
    );
}

const ToolsContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
`;

const ToolGroupContainer = styled.div`
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    overflow: hidden;
`;

const GroupHeader = styled.div<{ $expanded?: boolean }>`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.375rem 0.5rem;
    background: var(--ttw-foreground);
    cursor: pointer;
    border-bottom: ${props => props.$expanded ? '1px solid var(--bs-border-color)' : 'none'};

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const GroupHeaderLeft = styled.div`
    display: flex;
    align-items: center;
    gap: 0.375rem;
`;

const GroupTitle = styled.div`
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
`;

const ToolCount = styled.span`
    color: var(--bs-secondary);
    font-size: 1rem;
`;

const GroupActions = styled.div`
    display: flex;
    align-items: center;
`;

const ToolsList = styled.div`
    padding: 0.125rem 0.25rem 0.25rem;
`;

const ToolItem = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.25rem 0.5rem;
    border-radius: var(--bs-border-radius);
    
    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const ToolInfo = styled.div`
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
`;

const ToolName = styled.div`
    font-weight: 500;
    min-width: 100px;
    font-size: 1rem;
`;

const ToolDescription = styled.div`
    font-size: 1rem;
    color: var(--bs-secondary);
    flex: 1;
`;

const ToolToggle = styled.div`
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
`;

const DeleteButton = styled.button`
    background: none;
    border: none;
    color: var(--bs-secondary);
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
    border-radius: var(--bs-border-radius);
    display: flex;
    align-items: center;
    
    &:hover {
        color: var(--bs-danger);
        background: var(--ttw-box-hover-background);
    }
`;
